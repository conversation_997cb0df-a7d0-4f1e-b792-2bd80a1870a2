<template>
  <el-drawer
    v-model="visible"
    title="打印标签预览"
    direction="rtl"
    size="70%"
    destroy-on-close
  >
    <template #header>
      <div class="drawer-header">
        <span>打印标签预览</span>
        <div class="drawer-actions">
          <el-button type="primary" :icon="Printer" @click="handlePrint" :loading="printing">
            打印
          </el-button>
          <el-button :icon="Download" @click="handleDownload" :loading="downloading">
            下载
          </el-button>
        </div>
      </div>
    </template>

    <div v-loading="loading">
      <el-alert
        :title="`共 ${printData?.totalCount || 0} 条记录待打印，模板：${printData?.templateName || '未知'}`"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 16px"
      />

      <div class="preview-wrapper">
        <!-- 显示预览状态信息 -->
        <div class="preview-status">
          <el-tag v-if="hasTemplateHtml" type="success" size="small">
            <el-icon><Check /></el-icon>
            使用模板HTML预览
          </el-tag>
          <el-tag v-else type="warning" size="small">
            <el-icon><Warning /></el-icon>
            使用降级预览（建议重新保存模板）
          </el-tag>
        </div>

        <!-- 使用模板HTML进行预览 -->
        <div v-if="hasTemplateHtml">
          <div
            v-for="(template, index) in printData?.processedTemplates || []"
            :key="index"
            class="template-preview"
          >
            <div class="template-header">
              <span>模板预览 {{ index + 1 }}</span>
              <el-tag size="small" type="info">{{ printData?.templateName }}</el-tag>
            </div>
            <div
              class="template-content"
              v-html="processTemplateHtml(template.templateHtml)"
            ></div>
          </div>
        </div>

        <!-- 降级预览：如果没有模板HTML，显示简化的数据预览 -->
        <div v-else class="fallback-preview">
          <el-alert
            title="当前使用降级预览模式"
            description="该模板没有保存HTML内容，建议在模板设计器中重新保存模板以获得更准确的预览效果。"
            type="warning"
            :closable="false"
            show-icon
            style="margin-bottom: 16px"
          />

          <div
            v-for="(item, index) in printData?.items || []"
            :key="item.id || index"
            class="preview-item"
          >
            <div class="item-header">第 {{ index + 1 }} 条记录</div>
            <div class="item-content">
              <div class="field-row">
                <span class="field-label">钱币编号：</span>
                <span class="field-value">{{ item.serialNumber || '-' }}</span>
              </div>
              <div class="field-row">
                <span class="field-label">钱币名称：</span>
                <span class="field-value">{{ item.coinName || '-' }}</span>
              </div>
              <div class="field-row">
                <span class="field-label">银行名称：</span>
                <span class="field-value">{{ item.bankName || '-' }}</span>
              </div>
              <div class="field-row">
                <span class="field-label">评级分数：</span>
                <span class="field-value">{{ item.gradeScore || '-' }}</span>
              </div>
              <div class="field-row">
                <span class="field-label">客户姓名：</span>
                <span class="field-value">{{ item.customerName || '-' }}</span>
              </div>
              <div class="field-row">
                <span class="field-label">二维码：</span>
                <span class="field-value">{{ item.diyCode || '-' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空数据提示 -->
        <div v-if="!printData?.items?.length && !printData?.processedTemplates?.length" class="empty-preview">
          <el-empty description="暂无预览数据" />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { Printer, Download, Check, Warning } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';

let hiprint = null;

const props = defineProps({
  modelValue: Boolean,
  printData: Object
});

const emit = defineEmits(['update:modelValue', 'confirm-print']);

const loading = ref(false);
const printing = ref(false);
const downloading = ref(false);
const hiprintTemplate = ref(null);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 检查是否有模板HTML
const hasTemplateHtml = computed(() => {
  return props.printData?.processedTemplates?.some(template =>
    template.templateHtml && template.templateHtml.trim()
  );
});

// 处理模板HTML，确保安全性和正确性
const processTemplateHtml = (html) => {
  if (!html || typeof html !== 'string') {
    return '<div class="error-message">模板HTML格式错误</div>';
  }

  // 基本的HTML清理和处理
  let processedHtml = html.trim();

  // 如果HTML不包含基本结构，添加容器
  if (!processedHtml.includes('<div') && !processedHtml.includes('<table')) {
    processedHtml = `<div class="template-container">${processedHtml}</div>`;
  }

  return processedHtml;
};

// 初始化hiprint
const initHiprint = async () => {
  try {
    const hiprintModule = await import('vue-plugin-hiprint');
    hiprint = hiprintModule.hiprint || hiprintModule.default || hiprintModule;
    console.log('hiprint初始化成功');
  } catch (error) {
    console.error('hiprint初始化失败:', error);
    EleMessage.error('打印组件初始化失败');
  }
};

// 创建打印模板
const createPrintTemplate = () => {
  if (!hiprint || !props.printData?.processedTemplates?.length) {
    return;
  }

  try {
    const template = props.printData.processedTemplates[0];
    hiprintTemplate.value = new hiprint.PrintTemplate({
      template: template
    });
    console.log('打印模板创建成功');
  } catch (error) {
    console.error('创建打印模板失败:', error);
    EleMessage.error('创建打印模板失败');
  }
};

// 处理打印
const handlePrint = () => {
  if (!hiprintTemplate.value) {
    EleMessage.error('打印模板未准备好');
    return;
  }

  printing.value = true;

  try {
    hiprintTemplate.value.print({}, {}, {
      callback: () => {
        console.log('打印窗口已打开');
        printing.value = false;
      }
    });

    emit('confirm-print', { ...props.printData, action: 'print' });
  } catch (error) {
    console.error('打印失败:', error);
    EleMessage.error('打印失败');
    printing.value = false;
  }
};

// 处理下载
const handleDownload = () => {
  if (!hiprintTemplate.value) {
    EleMessage.error('打印模板未准备好');
    return;
  }

  downloading.value = true;

  try {
    hiprintTemplate.value.toPdf();
    emit('confirm-print', { ...props.printData, action: 'download' });
  } catch (error) {
    console.error('下载失败:', error);
    EleMessage.error('下载失败');
  } finally {
    downloading.value = false;
  }
};

// 监听抽屉显示状态
watch(visible, (newVal) => {
  if (newVal && props.printData?.processedTemplates?.length) {
    nextTick(createPrintTemplate);
  }
});

// 组件挂载
onMounted(initHiprint);
</script>

<style scoped>
.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.drawer-actions {
  display: flex;
  gap: 8px;
}

.preview-wrapper {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.preview-status {
  margin-bottom: 16px;
  text-align: center;
}

.template-preview {
  background: white;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  font-weight: 500;
}

.template-content {
  padding: 16px;
  overflow: auto;
  min-height: 200px;
}

.fallback-preview .preview-item {
  background: white;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.empty-preview {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 4px;
}

.error-message {
  padding: 20px;
  text-align: center;
  color: #f56c6c;
  background: #fef0f0;
  border-radius: 4px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.item-header {
  font-weight: bold;
  color: #409eff;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.item-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.field-row {
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.field-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.field-value {
  color: #333;
  flex: 1;
}
</style>
