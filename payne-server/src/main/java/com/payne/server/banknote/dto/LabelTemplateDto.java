package com.payne.server.banknote.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 标签模板数据传输对象
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
public class LabelTemplateDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 模板类型
     */
    private String templateType;
    
    /**
     * 布局配置JSON
     */
    private String layoutConfig;
    
    /**
     * 字段映射JSON
     */
    private String fieldMapping;

    /**
     * 模板HTML内容
     */
    private String templateHtml;

    /**
     * 是否默认模板
     */
    private Boolean isDefault;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建用户
     */
    private String createUser;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新用户
     */
    private String updateUser;
}