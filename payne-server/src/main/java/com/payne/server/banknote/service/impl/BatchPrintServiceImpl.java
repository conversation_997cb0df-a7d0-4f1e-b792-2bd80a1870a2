package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.payne.server.banknote.entity.LabelTemplate;
import com.payne.server.banknote.entity.PjOSendform;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.service.BatchPrintService;
import com.payne.server.banknote.service.PjOSendformItemService;
import com.payne.server.banknote.service.PjOSendformService;
import com.payne.server.banknote.service.LabelDesignService;
import com.payne.server.banknote.vo.BatchPrintCoinVO;
import com.payne.server.banknote.vo.PrintAuditResultVO;
import com.payne.server.banknote.vo.PrintDataVO;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量打印服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-12
 */
@Slf4j
@Service
@AllArgsConstructor
public class BatchPrintServiceImpl implements BatchPrintService {

    private final PjOSendformItemService pjOSendformItemService;
    private final PjOSendformService pjOSendformService;
    private final LabelDesignService labelDesignService;

    @Override
    public IPage<BatchPrintCoinVO> convertToVOPage(IPage<PjOSendformItem> page) {
        IPage<BatchPrintCoinVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        
        List<BatchPrintCoinVO> voList = page.getRecords().stream().map(item -> {
            BatchPrintCoinVO vo = new BatchPrintCoinVO();
            BeanUtils.copyProperties(item, vo);

            // 直接设置ID字段（String类型）
            vo.setId(item.getId());

            // 设置审核状态（暂时设为已审核，实际需要根据业务逻辑设置）
            vo.setAuditStatus(1);

            // 设置送评单号
            vo.setSendformNumber(item.getSendnum());
            
            // 组合钱币名称
            StringBuilder coinName = new StringBuilder();
            if (StringUtils.hasText(item.getCoinName1())) {
                coinName.append(item.getCoinName1());
            }
            if (StringUtils.hasText(item.getCoinName2())) {
                if (coinName.length() > 0) coinName.append(" ");
                coinName.append(item.getCoinName2());
            }
            if (StringUtils.hasText(item.getCoinName3())) {
                if (coinName.length() > 0) coinName.append(" ");
                coinName.append(item.getCoinName3());
            }
            vo.setCoinName(coinName.toString());
            
            // 设置其他字段
            vo.setVersion(item.getVersion());
            vo.setAdditionalInfo(item.getSpecialLabel());
            vo.setYearInfo(item.getYearInfo());
            vo.setGradeLevel(item.getRank());
            vo.setWeight(item.getCoinWeight() != null ? item.getCoinWeight().toString() : "");
            vo.setSize(item.getCoinSize());
            // 将String类型的gradeScore转换为Integer
            try {
                if (item.getGradeScore() != null && !item.getGradeScore().trim().isEmpty()) {
                    // 尝试从字符串中提取数字
                    String scoreStr = item.getGradeScore().replaceAll("[^0-9]", "");
                    if (!scoreStr.isEmpty()) {
                        vo.setGradeScore(Integer.parseInt(scoreStr));
                    }
                }
            } catch (NumberFormatException e) {
                vo.setGradeScore(null);
            }
            // 通过送评单号查询客户姓名
            if (item.getSendnum() != null) {
                LambdaQueryWrapper<PjOSendform> sendformWrapper = new LambdaQueryWrapper<>();
                sendformWrapper.eq(PjOSendform::getSendnum, item.getSendnum());
                PjOSendform sendform = pjOSendformService.getOne(sendformWrapper);
                if (sendform != null) {
                    vo.setCustomerName(sendform.getRname() != null ? sendform.getRname() : "");
                } else {
                    vo.setCustomerName("");
                }
            } else {
                vo.setCustomerName("");
            }
            vo.setFee(item.getGradeFee());
            vo.setCoinType(item.getCoinType());
            vo.setBankName(item.getBankName());

            return vo;
        }).collect(Collectors.toList());
        
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public PrintAuditResultVO checkAuditStatus(List<String> coinIds) {
        PrintAuditResultVO result = new PrintAuditResultVO();
        
        // 查询钱币信息
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        List<String> validIds = coinIds.stream()
                .filter(Objects::nonNull)
                .filter(id -> !id.trim().isEmpty())
                .collect(Collectors.toList());

        if (validIds.isEmpty()) {
            result.setTotalCount(0);
            result.setUnauditedCount(0);
            result.setUnauditedSendforms(new ArrayList<>());
            return result;
        }

        wrapper.in(PjOSendformItem::getId, validIds);
        List<PjOSendformItem> coins = pjOSendformItemService.list(wrapper);
        
        result.setTotalCount(coins.size());
        
        // 通过送评单号查询审核状态
        Set<String> sendnums = coins.stream()
                .map(PjOSendformItem::getSendnum)
                .filter(StringUtils::hasText)
                .collect(Collectors.toSet());

        List<String> unauditedSendforms = new ArrayList<>();
        int unauditedCount = 0;

        if (!sendnums.isEmpty()) {
            LambdaQueryWrapper<PjOSendform> sendformWrapper = new LambdaQueryWrapper<>();
            sendformWrapper.in(PjOSendform::getSendnum, sendnums);
            List<PjOSendform> sendforms = pjOSendformService.list(sendformWrapper);

            for (PjOSendform sendform : sendforms) {
                if (sendform.getCheckStatus() == null || sendform.getCheckStatus() != 1) {
                    unauditedSendforms.add(sendform.getSendnum());
                    // 统计该送评单下的钱币数量
                    long coinCount = coins.stream()
                            .filter(coin -> sendform.getSendnum().equals(coin.getSendnum()))
                            .count();
                    unauditedCount += coinCount;
                }
            }
        }

        result.setUnauditedCount(unauditedCount);
        result.setAuditedCount(coins.size() - unauditedCount);
        result.setUnauditedSendforms(unauditedSendforms);
        
        if (result.getUnauditedSendforms() == null) {
            result.setUnauditedSendforms(new ArrayList<>());
        }
        
        return result;
    }





    @Override
    public PrintDataVO generateCustomTemplatePrintData(List<String> coinIds, String templateId,
                                                      Integer conversionType, String printType) {
        // 获取自定义模板
        LabelTemplate template = labelDesignService.getById(templateId);
        if (template == null) {
            throw new RuntimeException("模板不存在: " + templateId);
        }

        // 解析模板配置
        Map<String, Object> layoutConfig = parseLayoutConfig(template.getLayoutConfig());
        Map<String, List<String>> fieldMapping = parseFieldMapping(template.getFieldMapping());

        // 查询钱币数据
        List<PjOSendformItem> coins = getCoinsByIds(coinIds);

        // 处理模板字段替换 - 为每个钱币生成对应的模板
        List<Map<String, Object>> processedTemplates = processTemplateFieldReplacement(layoutConfig, coins);

        // 为每个处理后的模板添加HTML内容
        addTemplateHtmlToProcessedTemplates(processedTemplates, template.getTemplateHtml());

        // 转换为自定义模板数据
        PrintDataVO printData = new PrintDataVO();
        printData.setItems(convertToCustomTemplateData(coins, fieldMapping));
        printData.setTotalCount(coins.size());
        printData.setTemplateId(templateId);
        printData.setTemplateName(template.getTemplateName());
        printData.setLayoutConfig(layoutConfig);
        printData.setProcessedTemplates(processedTemplates); // 添加处理后的模板列表
        printData.setFieldMapping(fieldMapping);
        printData.setCoinIds(coinIds);
        printData.setConversionType(conversionType);
        printData.setPrintType(printType);

        return printData;
    }

    private Map<String, Object> parseLayoutConfig(String layoutConfigJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(layoutConfigJson, Map.class);
        } catch (Exception e) {
            log.warn("解析布局配置失败: {}", e.getMessage());
            return getDefaultLayoutConfig();
        }
    }

    private Map<String, List<String>> parseFieldMapping(String fieldMappingJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            TypeReference<Map<String, List<String>>> typeRef = new TypeReference<Map<String, List<String>>>() {};
            return mapper.readValue(fieldMappingJson, typeRef);
        } catch (Exception e) {
            log.warn("解析字段映射失败: {}", e.getMessage());
            return getDefaultFieldMapping();
        }
    }

    private List<PjOSendformItem> getCoinsByIds(List<String> coinIds) {
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        List<String> validIds = coinIds.stream()
                .filter(Objects::nonNull)
                .filter(id -> !id.trim().isEmpty())
                .collect(Collectors.toList());
        
        if (validIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        wrapper.in(PjOSendformItem::getId, validIds);
        return pjOSendformItemService.list(wrapper);
    }

    private List<BatchPrintCoinVO> convertToCustomTemplateData(List<PjOSendformItem> coins, 
                                                              Map<String, List<String>> fieldMapping) {
        return coins.stream().map(coin -> {
            BatchPrintCoinVO vo = convertToVOBasic(coin);

            // 根据字段映射添加自定义字段数据
            Map<String, Object> customFields = new HashMap<>();
            fieldMapping.forEach((zoneId, fields) -> {
                Map<String, Object> zoneData = new HashMap<>();
                fields.forEach(fieldName -> {
                    Object fieldValue = getFieldValue(coin, fieldName);
                    zoneData.put(fieldName, fieldValue);
                });
                customFields.put(zoneId, zoneData);
            });

            vo.setCustomFields(customFields);
            return vo;
        }).collect(Collectors.toList());
    }


    private Object getFieldValue(PjOSendformItem coin, String fieldName) {
        switch (fieldName) {
            case "bankName": return coin.getBankName();
            case "coinName": return getCombinedCoinName(coin);
            case "coinName1": return coin.getCoinName1();
            case "coinName2": return coin.getCoinName2();
            case "coinName3": return coin.getCoinName3();
            case "yearInfo": return coin.getYearInfo();
            case "serialNumber": return coin.getSerialNumber();
            case "version": return coin.getVersion();
            case "serialNumberWithVersion": return formatSerialNumberWithVersion(coin);
            case "gradeScore": return coin.getGradeScore();
            case "gradeLevel": return coin.getRank(); // 使用rank字段作为gradeLevel
            case "specialMark": return coin.getSpecialMark();
            case "authenticity": return coin.getAuthenticity();
            case "diyCode": return coin.getDiyCode();
            case "qrcode": return coin.getDiyCode(); // 二维码内容使用送评条码
            case "qrCodeContent": return coin.getDiyCode();
            case "customerName": return getCustomerName(coin);
            case "weight": return coin.getCoinWeight() != null ? coin.getCoinWeight().toString() : "";
            case "size": return coin.getCoinSize();
            case "coinType": return coin.getCoinType();
            default: return "";
        }
    }

    /**
     * 获取组合的钱币名称
     */
    private String getCombinedCoinName(PjOSendformItem coin) {
        StringBuilder coinName = new StringBuilder();
        if (StringUtils.hasText(coin.getCoinName1())) {
            coinName.append(coin.getCoinName1());
        }
        if (StringUtils.hasText(coin.getCoinName2())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(coin.getCoinName2());
        }
        if (StringUtils.hasText(coin.getCoinName3())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(coin.getCoinName3());
        }
        return coinName.toString();
    }

    /**
     * 获取客户姓名
     */
    private String getCustomerName(PjOSendformItem coin) {
        if (coin.getSendnum() != null) {
            LambdaQueryWrapper<PjOSendform> sendformWrapper = new LambdaQueryWrapper<>();
            sendformWrapper.eq(PjOSendform::getSendnum, coin.getSendnum());
            PjOSendform sendform = pjOSendformService.getOne(sendformWrapper);
            if (sendform != null) {
                return sendform.getRname() != null ? sendform.getRname() : "";
            }
        }
        return "";
    }

    /**
     * 处理模板字段替换 - 为每个钱币生成对应的模板
     * @param layoutConfig 原始模板配置
     * @param coins 钱币数据列表
     * @return 处理后的模板列表
     */
    private List<Map<String, Object>> processTemplateFieldReplacement(Map<String, Object> layoutConfig, List<PjOSendformItem> coins) {
        List<Map<String, Object>> processedTemplates = new ArrayList<>();

        for (PjOSendformItem coin : coins) {
            try {
                // 深拷贝模板配置
                ObjectMapper mapper = new ObjectMapper();
                String templateJson = mapper.writeValueAsString(layoutConfig);
                Map<String, Object> templateCopy = mapper.readValue(templateJson, Map.class);

                // 替换模板中的字段变量
                Map<String, Object> processedTemplate = replaceTemplateFields(templateCopy, coin);
                processedTemplates.add(processedTemplate);

                log.debug("为钱币 {} 处理模板字段替换完成", coin.getId());
            } catch (Exception e) {
                log.error("处理钱币 {} 的模板字段替换失败: {}", coin.getId(), e.getMessage());
                // 如果处理失败，使用原始模板
                processedTemplates.add(layoutConfig);
            }
        }

        return processedTemplates;
    }

    /**
     * 替换模板中的字段变量
     * @param template 模板配置
     * @param coin 钱币数据
     * @return 处理后的模板
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> replaceTemplateFields(Map<String, Object> template, PjOSendformItem coin) {
        // 处理panels数组
        if (template.containsKey("panels") && template.get("panels") instanceof List) {
            List<Map<String, Object>> panels = (List<Map<String, Object>>) template.get("panels");

            for (Map<String, Object> panel : panels) {
                if (panel.containsKey("printElements") && panel.get("printElements") instanceof List) {
                    List<Map<String, Object>> printElements = (List<Map<String, Object>>) panel.get("printElements");

                    for (Map<String, Object> element : printElements) {
                        if (element.containsKey("options") && element.get("options") instanceof Map) {
                            Map<String, Object> options = (Map<String, Object>) element.get("options");

                            // 处理field字段
                            if (options.containsKey("field") && options.get("field") instanceof String) {
                                String fieldValue = (String) options.get("field");
                                String replacedValue = processComplexFieldCombination(fieldValue, coin);
                                options.put("field", replacedValue);

                                // 如果title为空或包含变量，也进行替换
                                if (!options.containsKey("title") ||
                                    (options.get("title") instanceof String && ((String) options.get("title")).contains("{"))) {
                                    options.put("title", replacedValue);
                                }
                            }

                            // 处理title字段
                            if (options.containsKey("title") && options.get("title") instanceof String) {
                                String titleValue = (String) options.get("title");
                                String replacedTitle = processComplexFieldCombination(titleValue, coin);
                                options.put("title", replacedTitle);
                            }

                            // 处理text字段
                            if (options.containsKey("text") && options.get("text") instanceof String) {
                                String textValue = (String) options.get("text");
                                String replacedText = processComplexFieldCombination(textValue, coin);
                                options.put("text", replacedText);
                            }
                        }
                    }
                }
            }
        }

        return template;
    }

    /**
     * 替换字符串中的字段变量
     * @param text 包含变量的文本
     * @param coin 钱币数据
     * @return 替换后的文本
     */
    private String replaceFieldVariables(String text, PjOSendformItem coin) {
        if (text == null || !text.contains("{")) {
            return text;
        }

        String result = text;

        // 使用正则表达式匹配 {fieldName} 格式的变量
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\{(\\w+)\\}");
        java.util.regex.Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            String fullMatch = matcher.group(0); // 完整匹配，如 {bankName}
            String fieldName = matcher.group(1); // 字段名，如 bankName

            // 获取字段值
            Object fieldValue = getFieldValue(coin, fieldName);
            String replacementValue = fieldValue != null ? fieldValue.toString() : "";

            // 替换变量
            result = result.replace(fullMatch, replacementValue);

            log.debug("字段替换: {} -> {}", fullMatch, replacementValue);
        }

        return result;
    }

    /**
     * 处理复杂字段组合（如 {serialNumber}-{version}）
     * @param text 包含多个变量的文本
     * @param coin 钱币数据
     * @return 替换后的文本
     */
    private String processComplexFieldCombination(String text, PjOSendformItem coin) {
        if (text == null || !text.contains("{")) {
            return text;
        }

        // 处理特殊的组合字段
        if (text.contains("{serialNumber}") && text.contains("{version}")) {
            String serialNumber = coin.getSerialNumber() != null ? coin.getSerialNumber() : "";
            String version = coin.getVersion() != null ? coin.getVersion() : "";

            // 如果两个字段都有值，用连接符连接；否则只显示有值的部分
            if (!serialNumber.isEmpty() && !version.isEmpty()) {
                return text.replace("{serialNumber}-{version}", serialNumber + "-" + version)
                          .replace("{serialNumber}", serialNumber)
                          .replace("{version}", version);
            } else if (!serialNumber.isEmpty()) {
                return text.replace("{serialNumber}-{version}", serialNumber)
                          .replace("{serialNumber}", serialNumber)
                          .replace("{version}", "");
            } else if (!version.isEmpty()) {
                return text.replace("{serialNumber}-{version}", version)
                          .replace("{serialNumber}", "")
                          .replace("{version}", version);
            } else {
                return text.replace("{serialNumber}-{version}", "")
                          .replace("{serialNumber}", "")
                          .replace("{version}", "");
            }
        }

        // 对于其他情况，使用通用的字段替换
        return replaceFieldVariables(text, coin);
    }

    /**
     * 测试字段替换功能
     * 用于验证模板字段替换是否正常工作
     */
    public void testFieldReplacement() {
        // 创建测试用的钱币数据
        PjOSendformItem testCoin = new PjOSendformItem();
        testCoin.setId("test001");
        testCoin.setBankName("中国人民银行");
        testCoin.setCoinName1("2023年熊猫金币");
        testCoin.setSerialNumber("PD2023001");
        testCoin.setVersion("普制版");
        testCoin.setGradeScore("MS68");
        testCoin.setSpecialMark("EPQ");
        testCoin.setDiyCode("DIY2023001");

        // 测试各种字段替换
        log.info("=== 字段替换测试 ===");
        log.info("bankName: {} -> {}", "{bankName}", replaceFieldVariables("{bankName}", testCoin));
        log.info("gradeScore: {} -> {}", "{gradeScore}", replaceFieldVariables("{gradeScore}", testCoin));
        log.info("qrcode: {} -> {}", "{qrcode}", replaceFieldVariables("{qrcode}", testCoin));
        log.info("复杂字段: {} -> {}", "{serialNumber}-{version}",
                processComplexFieldCombination("{serialNumber}-{version}", testCoin));

        // 测试模板处理
        Map<String, Object> testTemplate = createTestTemplate();
        Map<String, Object> processedTemplate = replaceTemplateFields(testTemplate, testCoin);

        log.info("原始模板: {}", testTemplate);
        log.info("处理后模板: {}", processedTemplate);
    }

    /**
     * 创建测试模板
     */
    private Map<String, Object> createTestTemplate() {
        Map<String, Object> template = new HashMap<>();
        List<Map<String, Object>> panels = new ArrayList<>();
        Map<String, Object> panel = new HashMap<>();

        List<Map<String, Object>> printElements = new ArrayList<>();

        // 创建测试元素
        Map<String, Object> element1 = new HashMap<>();
        Map<String, Object> options1 = new HashMap<>();
        options1.put("field", "{bankName}");
        options1.put("title", "银行名称");
        element1.put("options", options1);
        printElements.add(element1);

        Map<String, Object> element2 = new HashMap<>();
        Map<String, Object> options2 = new HashMap<>();
        options2.put("field", "{serialNumber}-{version}");
        options2.put("title", "钱币编号-版别");
        element2.put("options", options2);
        printElements.add(element2);

        panel.put("printElements", printElements);
        panels.add(panel);
        template.put("panels", panels);

        return template;
    }

    /**
     * 格式化编号-版别组合字段
     */
    private String formatSerialNumberWithVersion(PjOSendformItem coin) {
        String serialNumber = coin.getSerialNumber();
        String version = coin.getVersion();

        if (serialNumber != null && !serialNumber.trim().isEmpty() &&
            version != null && !version.trim().isEmpty()) {
            return serialNumber + " - " + version;
        } else if (serialNumber != null && !serialNumber.trim().isEmpty()) {
            return serialNumber;
        } else if (version != null && !version.trim().isEmpty()) {
            return version;
        }
        return "";
    }

    private Map<String, Object> getDefaultLayoutConfig() {
        Map<String, Object> config = new HashMap<>();
        Map<String, Object> canvas = new HashMap<>();
        canvas.put("width", 200);
        canvas.put("height", 25);
        config.put("canvas", canvas);
        
        List<Map<String, Object>> zones = new ArrayList<>();
        // 公司Logo区域
        Map<String, Object> logoZone = new HashMap<>();
        logoZone.put("id", "logo");
        logoZone.put("name", "公司Logo");
        logoZone.put("x", 0);
        logoZone.put("y", 0);
        logoZone.put("width", 30);
        logoZone.put("height", 25);
        zones.add(logoZone);
        
        // 钱币信息区域
        Map<String, Object> coinZone = new HashMap<>();
        coinZone.put("id", "coinInfo");
        coinZone.put("name", "钱币信息");
        coinZone.put("x", 30);
        coinZone.put("y", 0);
        coinZone.put("width", 100);
        coinZone.put("height", 25);
        zones.add(coinZone);
        
        // 评级信息区域
        Map<String, Object> gradeZone = new HashMap<>();
        gradeZone.put("id", "gradeInfo");
        gradeZone.put("name", "评级信息");
        gradeZone.put("x", 130);
        gradeZone.put("y", 0);
        gradeZone.put("width", 70);
        gradeZone.put("height", 25);
        zones.add(gradeZone);
        
        config.put("zones", zones);
        return config;
    }

    private Map<String, List<String>> getDefaultFieldMapping() {
        Map<String, List<String>> mapping = new HashMap<>();
        mapping.put("coinInfo", Arrays.asList("bankName", "coinName1", "serialNumber"));
        mapping.put("gradeInfo", Arrays.asList("gradeScore", "gradeLevel"));
        return mapping;
    }



    @Override
    public Map<String, Object> generatePreviewData(List<String> coinIds, String templateId, Integer conversionType) {
        Map<String, Object> previewData = new HashMap<>();

        PrintDataVO printData = generateCustomTemplatePrintData(coinIds, templateId, conversionType, null);
        previewData.put("printData", printData);

        // 添加预览配置信息
        previewData.put("previewUrl", "/api/batch-print/preview-report");
        previewData.put("totalCount", coinIds.size());
        previewData.put("templateId", templateId);
        previewData.put("conversionType", conversionType);

        return previewData;
    }

    @Override
    public void executePrint(List<String> coinIds, String templateId, Integer conversionType, String action, HttpServletResponse response) {
        try {
            // 生成打印数据
            PrintDataVO printData = generateCustomTemplatePrintData(coinIds, templateId, conversionType, action);

            // 设置响应头
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition",
                    "attachment; filename=batch_print_" + System.currentTimeMillis() + ".pdf");

            // 这里应该调用报表生成服务生成PDF
            // 目前先返回一个模拟的PDF内容
            String pdfContent = generateMockPDF(printData);

            OutputStream outputStream = response.getOutputStream();
            outputStream.write(pdfContent.getBytes());
            outputStream.flush();
            outputStream.close();

        } catch (IOException e) {
            throw new RuntimeException("生成打印文件失败", e);
        }
    }

    /**
     * 生成模拟PDF内容
     */
    private String generateMockPDF(PrintDataVO printData) {
        StringBuilder content = new StringBuilder();
        content.append("批量打印标签\n");
        content.append("模板名称：").append(printData.getTemplateName()).append("\n");
        content.append("转换类型：").append(printData.getConversionTypeName()).append("\n");
        content.append("打印数量：").append(printData.getTotalCount()).append("\n\n");

        for (BatchPrintCoinVO coin : printData.getItems()) {
            content.append("钱币编号：").append(coin.getSerialNumber()).append("\n");
            content.append("钱币名称：").append(coin.getCoinName()).append("\n");
            content.append("评级分数：").append(coin.getGradeScore()).append("\n");
            content.append("客户姓名：").append(coin.getCustomerName()).append("\n");
            content.append("---\n");
        }

        return content.toString();
    }



    /**
     * 转换为基础VO对象
     */
    private BatchPrintCoinVO convertToVOBasic(PjOSendformItem item) {
        BatchPrintCoinVO vo = new BatchPrintCoinVO();
        BeanUtils.copyProperties(item, vo);
        
        vo.setId(item.getId());
        vo.setAuditStatus(1);
        vo.setSendformNumber(item.getSendnum());
        
        // 组合钱币名称
        StringBuilder coinName = new StringBuilder();
        if (StringUtils.hasText(item.getCoinName1())) {
            coinName.append(item.getCoinName1());
        }
        if (StringUtils.hasText(item.getCoinName2())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(item.getCoinName2());
        }
        if (StringUtils.hasText(item.getCoinName3())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(item.getCoinName3());
        }
        vo.setCoinName(coinName.toString());
        
        // 设置其他字段
        vo.setVersion(item.getVersion());
        vo.setAdditionalInfo(item.getSpecialLabel());
        vo.setYearInfo(item.getYearInfo());
        vo.setGradeLevel(item.getRank());
        vo.setWeight(item.getCoinWeight() != null ? item.getCoinWeight().toString() : "");
        vo.setSize(item.getCoinSize());
        
        // 转换评级分数
        try {
            if (item.getGradeScore() != null && !item.getGradeScore().trim().isEmpty()) {
                String scoreStr = item.getGradeScore().replaceAll("[^0-9]", "");
                if (!scoreStr.isEmpty()) {
                    vo.setGradeScore(Integer.parseInt(scoreStr));
                }
            }
        } catch (NumberFormatException e) {
            vo.setGradeScore(null);
        }
        
        // 查询客户姓名
        if (item.getSendnum() != null) {
            LambdaQueryWrapper<PjOSendform> sendformWrapper = new LambdaQueryWrapper<>();
            sendformWrapper.eq(PjOSendform::getSendnum, item.getSendnum());
            PjOSendform sendform = pjOSendformService.getOne(sendformWrapper);
            if (sendform != null) {
                vo.setCustomerName(sendform.getRname() != null ? sendform.getRname() : "");
            } else {
                vo.setCustomerName("");
            }
        } else {
            vo.setCustomerName("");
        }
        
        vo.setFee(item.getGradeFee());
        vo.setCoinType(item.getCoinType());
        vo.setBankName(item.getBankName());
        
        return vo;
    }


}
