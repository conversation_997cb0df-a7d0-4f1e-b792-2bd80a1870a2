package com.payne.server.banknote.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 标签模板实体类
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Entity
@Table(name = "LABEL_TEMPLATE")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("LABEL_TEMPLATE")
public class LabelTemplate implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    @Column(name = "TEMPLATE_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("TEMPLATE_NAME")
    private String templateName;
    
    @Column(name = "TEMPLATE_TYPE", columnDefinition = "VARCHAR2(20)")
    @TableField("TEMPLATE_TYPE")
    private String templateType;
    
    @Column(name = "LAYOUT_CONFIG", columnDefinition = "CLOB")
    @TableField("LAYOUT_CONFIG")
    private String layoutConfig;
    
    @Column(name = "FIELD_MAPPING", columnDefinition = "CLOB")
    @TableField("FIELD_MAPPING")
    private String fieldMapping;
    
    @Column(name = "COLOR_CONFIG", columnDefinition = "CLOB")
    @TableField("COLOR_CONFIG")
    private String colorConfig;
    
    @Column(name = "PAGE_SETTINGS", columnDefinition = "CLOB")
    @TableField("PAGE_SETTINGS")
    private String pageSettings;

    @Column(name = "TEMPLATE_HTML", columnDefinition = "CLOB")
    @TableField("TEMPLATE_HTML")
    private String templateHtml;

    @Column(name = "IS_DEFAULT", columnDefinition = "NUMBER(1)")
    @TableField("IS_DEFAULT")
    private Boolean isDefault;
    
    @Column(name = "CREATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("CREATE_USER")
    private String createUser;
    
    @Column(name = "CREATE_TIME")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    @Column(name = "UPDATE_TIME")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    @Column(name = "STATUS", columnDefinition = "VARCHAR2(10)")
    @TableField("STATUS")
    private String status;
}